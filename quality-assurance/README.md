# Quality Assurance & Testing Agents

This category contains agents specialized in ensuring code quality and system reliability.

## Agents in this Category

### code-reviewer
**Purpose**: Perform comprehensive code reviews and quality assessments
- Conducts thorough code reviews for quality and standards
- Identifies potential bugs, security issues, and improvements
- Ensures adherence to coding standards and best practices
- Provides constructive feedback for code improvement

### test-suite-generator
**Purpose**: Create comprehensive test suites and testing strategies
- Generates unit, integration, and end-to-end tests
- Creates testing strategies and test plans
- Ensures comprehensive test coverage
- Implements automated testing frameworks

### qa-coordinator
**Purpose**: Coordinate quality assurance processes and standards
- Establishes QA processes and quality gates
- Coordinates testing activities across teams
- Manages quality metrics and reporting
- Ensures consistent quality standards

### uat-coordinator
**Purpose**: Manage user acceptance testing and stakeholder validation
- Coordinates user acceptance testing with stakeholders
- Manages UAT processes and feedback collection
- Ensures business requirements are met
- Facilitates stakeholder sign-off and approval

## Usage Patterns

These agents work together to ensure quality:
1. **qa-coordinator** establishes quality processes
2. **code-reviewer** reviews code for quality
3. **test-suite-generator** creates comprehensive tests
4. **uat-coordinator** validates with stakeholders

## Integration with Other Categories

- **Development**: Reviews and tests developed code
- **Requirements Analysis**: Validates against requirements
- **Security**: Coordinates security testing
- **Project Management**: Reports on quality metrics
