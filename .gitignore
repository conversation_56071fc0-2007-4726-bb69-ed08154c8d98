# Claude Code Sub-Agents Repository .gitignore

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Editor and IDE Files
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.emacs.d/
.sublime-*

# Temporary Files
*.tmp
*.temp
*.log
*.bak
*.backup
*.orig
*.rej

# Node.js (for any tooling or scripts)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
package-lock.json
yarn.lock

# Python (for any tooling or scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Documentation Build Files
docs/_build/
docs/build/
site/
.mkdocs.yml.bak

# Test Files and Coverage
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
htmlcov/

# Local Configuration Files
.env.local
.env.development.local
.env.test.local
.env.production.local
config.local.*
settings.local.*

# Cache Directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime Data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Local Development Files
scratch/
test-projects/
examples/local/
playground/

# User-specific agent customizations
*.local.md
*-custom.md
*-personal.md

# Backup files
*.backup
*.bak
*~

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Security and sensitive files
*.key
*.pem
*.p12
*.pfx
secrets/
.secrets/

# Database files
*.db
*.sqlite
*.sqlite3

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages
*.dmg
*.iso
*.jar
*.msi
*.msm
*.msp

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# Visual Studio
.vs/
*.user
*.userosscache
*.sln.docstates

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Mac
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*
