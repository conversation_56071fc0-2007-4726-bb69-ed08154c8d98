# Security & Compliance Agents

This category contains agents specialized in implementing robust security measures and ensuring compliance.

## Agents in this Category

### security-architect
**Purpose**: Design comprehensive security architectures and frameworks
- Designs end-to-end security architectures
- Creates security policies and governance frameworks
- Implements defense-in-depth strategies
- Ensures compliance with security standards (OWASP, ISO 27001, etc.)

### security-analyzer
**Purpose**: Analyze code and systems for security vulnerabilities
- Conducts security code reviews and vulnerability assessments
- Identifies security risks and potential attack vectors
- Performs penetration testing and security audits
- Provides remediation guidance for security issues

## Usage Patterns

These agents work together for comprehensive security:
1. **security-architect** designs security framework
2. **security-analyzer** validates implementation and identifies vulnerabilities

## Integration with Other Categories

- **Design & Architecture**: Integrates security into system design
- **Development**: Ensures secure coding practices
- **Quality Assurance**: Includes security testing in QA processes
- **Operations**: Implements security monitoring and incident response
