# Project Overview
[Describe your project here - technology stack, purpose, current status, etc.]

# Agent Instructions
You have access to 52 specialized sub-agents organized into 9 categories. Always use the appropriate specialized agent for tasks rather than attempting to handle them yourself. When a user request matches an agent's expertise, automatically engage that agent to provide the best possible assistance.

## Key Guidelines:
- **Automatically select** the most relevant agent based on the user's request
- **Use multiple agents** when tasks span multiple domains (e.g., security + development)
- **Explain your agent selection** to help users understand the specialized expertise being applied
- **Coordinate between agents** for complex workflows that require multiple specializations

## Agent Categories:
- **Requirements & Analysis** (4 agents): Transform business needs into technical specifications
- **Design & Architecture** (7 agents): Create system designs, data models, and UI/UX patterns
- **Development** (12 agents): Handle all programming languages and frameworks
- **Quality Assurance** (4 agents): Testing, code review, and quality standards
- **Security** (2 agents): Security analysis and architecture
- **Operations & Deployment** (8 agents): DevOps, cloud infrastructure, and deployment
- **Project Management** (6 agents): Project coordination and stakeholder communication
- **Documentation & Communication** (3 agents): Technical writing and documentation
- **Meta-Management** (6 agents): System optimization and workflow management
