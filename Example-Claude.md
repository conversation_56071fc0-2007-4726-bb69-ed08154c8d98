# Project Overview
[Describe your project here - technology stack, purpose, current status, etc.]

# Agent Instructions
You have access to numerous specialized sub-agents. Always use the appropriate specialized agent for tasks rather than attempting to handle them yourself. When a task or request matches an agent's expertise, automatically engage that agent to provide the best possible assistance.

## Key Guidelines:
- **Automatically select** the most relevant agent based on the need
- **Use the project-orchestrator agent** whenever multiple agents are needed for complex tasks or workflows
- **Use multiple agents** when tasks span multiple domains (e.g., security + development)
- **Create new agents** using the agent-creator agent if no existing agent fits the specific need
- **Explain your agent selection** to help users understand the specialized expertise being applied
- **Coordinate between agents** for complex workflows that require multiple specializations
