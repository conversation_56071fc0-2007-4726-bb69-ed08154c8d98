# Claude Code Agent Configuration

## 📋 Project Information
```
Project Name: [YOUR_PROJECT_NAME]
Technology Stack: [e.g., React, Node.js, Python, etc.]
Project Type: [e.g., Web App, API, Mobile App, etc.]
Current Phase: [e.g., Planning, Development, Testing, Production]
```

## 🤖 Available Agents

### Requirements & Analysis
```
Use the requirements-analyst agent to gather and analyze project requirements
```

```
Use the business-process-analyst agent to analyze and optimize business workflows
```

```
Use the requirements-validator agent to validate requirements completeness and feasibility
```

```
Use the user-story-generator agent to create detailed user stories and acceptance criteria
```

### Design & Architecture
```
Use the system-architect agent to design overall system architecture and technology stack
```

```
Use the data-architect agent to design data models, schemas, and integration strategies
```

```
Use the database-schema-designer agent to create efficient database schemas and migrations
```

```
Use the api-designer agent to design clean, RESTful APIs with proper specifications
```

```
Use the interface-designer agent to design user interfaces and user experience patterns
```

```
Use the design-research-agent agent to research current UI/UX trends and accessibility standards
```

```
Use the design-reviewer agent to review and validate system designs for quality
```

### Development
```
Use the python-developer agent for Python development with frameworks and best practices
```

```
Use the nodejs-developer agent for Node.js and JavaScript backend development
```

```
Use the nestjs-developer agent for NestJS framework development and enterprise-grade Node.js applications
```

```
Use the react-developer agent for React application development and component libraries
```

```
Use the nextjs-developer agent for Next.js and React frontend development with SSR/SSG
```

```
Use the golang-developer agent for Go language development and microservices
```

```
Use the ruby-developer agent for Ruby language development and scripting
```

```
Use the rails-developer agent for Ruby on Rails web application development
```

```
Use the sql-developer agent for database development and query optimization
```

```
Use the kubernetes-developer agent for Kubernetes application development and containerization
```

```
Use the code-refactoring-specialist agent to improve code quality, performance, and maintainability
```

```
Use the dependency-manager agent to manage project dependencies and version compatibility
```

```
Use the technical-research-analyst agent to research technologies and provide implementation guidance
```

### Quality Assurance
```
Use the code-reviewer agent for comprehensive code quality assessment and best practices
```

```
Use the test-suite-generator agent to create comprehensive testing strategies and test suites
```

```
Use the qa-coordinator agent to establish quality standards and coordinate testing efforts
```

```
Use the uat-coordinator agent to coordinate user acceptance testing and stakeholder validation
```

### Security
```
Use the security-analyzer agent for security vulnerability assessment and secure coding practices
```

```
Use the security-architect agent to design security frameworks and compliance strategies
```

### Operations & Deployment
```
Use the aws-solutions-architect agent for AWS cloud architecture and services
```

```
Use the kubernetes-administrator agent for Kubernetes cluster management and operations
```

```
Use the cicd-builder agent for CI/CD pipeline creation and automation
```

```
Use the deployment-ops-manager agent for production deployment and release management
```

```
Use the performance-optimizer agent for application performance tuning and optimization
```

```
Use the cost-optimizer agent for cloud cost analysis and optimization strategies
```

```
Use the resource-monitor agent for system monitoring and alerting setup
```

```
Use the arch-linux-sysadmin agent for Arch Linux system administration and configuration
```

### Project Management
```
Use the project-orchestrator agent to coordinate complex projects and manage overall development workflow
```

```
Use the project-planner agent to create detailed project plans, timelines, and resource allocation
```

```
Use the project-template-manager agent to create and manage reusable project templates
```

```
Use the stakeholder-communicator agent to manage stakeholder communication and reporting
```

```
Use the risk-manager agent to identify, assess, and mitigate project risks
```

```
Use the training-change-manager agent to manage training and change management processes
```

### Documentation & Communication
```
Use the documentation-generator agent to create comprehensive technical documentation
```

```
Use the markdown-writer agent to create README files, guides, and markdown documentation
```

```
Use the workflow-optimizer agent to analyze and optimize development workflows
```

### Meta-Management
```
Use the agent-creator agent to create new specialized agents for unique project needs
```

```
Use the context-manager agent to manage and optimize Claude Code context and memory
```

```
Use the git-manager agent for advanced Git operations and repository management
```

```
Use the memory-manager agent to manage long-term project memory and knowledge
```

```
Use the progress-tracker agent to track project progress and milestone completion
```

```
Use the session-continuity-manager agent to maintain context across long development sessions
```
