# Operations & Deployment Agents

This category contains agents specialized in deploying, monitoring, and maintaining production systems.

## Agents in this Category

### cicd-builder
**Purpose**: Create and optimize CI/CD pipelines and automation
- Designs and implements CI/CD pipelines
- Automates build, test, and deployment processes
- Optimizes pipeline performance and reliability
- Integrates with various deployment platforms

### deployment-ops-manager
**Purpose**: Manage deployment processes and production operations
- Manages production deployments and rollbacks
- Monitors system health and performance
- Handles incident response and troubleshooting
- Ensures operational reliability and uptime

### aws-solutions-architect
**Purpose**: Design and implement AWS cloud solutions
- Designs scalable AWS cloud architectures
- Implements cloud-native solutions and services
- Optimizes for cost, performance, and reliability
- Ensures AWS best practices and compliance

### performance-optimizer
**Purpose**: Analyze and optimize system performance
- Analyzes system performance bottlenecks
- Optimizes application and infrastructure performance
- Implements monitoring and alerting systems
- Provides performance tuning recommendations

### cost-optimizer
**Purpose**: Optimize cloud costs and resource utilization
- Analyzes cloud spending and resource usage
- Identifies cost optimization opportunities
- Implements cost-effective resource strategies
- Provides ongoing cost monitoring and recommendations

### resource-monitor
**Purpose**: Monitor system resources and performance metrics
- Monitors system resources and performance
- Sets up alerting and notification systems
- Tracks key performance indicators
- Provides operational insights and recommendations

### arch-linux-sysadmin
**Purpose**: Arch Linux system administration and configuration
- Expert in Arch Linux philosophy and package management
- Handles system configuration, service management with systemd
- Implements security hardening and performance optimization
- Provides Arch-specific troubleshooting and maintenance

### kubernetes-administrator
**Purpose**: Kubernetes cluster management and administration
- Expert in cluster setup, networking, and security configuration
- Handles RBAC, policies, and governance implementation
- Implements monitoring, logging, and observability solutions
- Provides cluster troubleshooting and performance optimization

## Usage Patterns

These agents support the full operational lifecycle:
1. **aws-solutions-architect** designs cloud infrastructure
2. **kubernetes-administrator** manages Kubernetes cluster operations
3. **cicd-builder** creates deployment automation
4. **deployment-ops-manager** manages production operations
5. **arch-linux-sysadmin** handles OS-level system administration
6. **performance-optimizer**, **cost-optimizer**, and **resource-monitor** provide ongoing optimization

## Integration with Other Categories

- **Development**: Deploys developed applications
- **Security**: Implements security in operations
- **Quality Assurance**: Integrates testing into deployment pipelines
- **Project Management**: Reports on operational metrics
