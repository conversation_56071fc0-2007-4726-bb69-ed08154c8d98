# Project Management & Coordination Agents

This category contains agents specialized in orchestrating complex projects and coordinating team efforts. These agents form the coordination backbone of the entire agent ecosystem, ensuring seamless project execution from inception to completion.

## Agents in this Category (6 agents)

### project-orchestrator
**Purpose**: Master coordinator for end-to-end project execution
- Coordinates entire project lifecycle from requirements to deployment
- Manages agent workflows and dependencies
- Provides high-level project oversight and decision-making
- Ensures project goals and timelines are met

### project-planner
**Purpose**: Create detailed project plans and milestone tracking
- Creates comprehensive project plans and schedules
- Defines milestones, deliverables, and dependencies
- Tracks project progress and identifies risks
- Provides project timeline and resource planning

### project-template-manager
**Purpose**: Manage reusable project templates and scaffolding
- Creates and maintains project templates
- Provides quick setup for common project patterns
- Ensures consistency across similar projects
- Manages template versioning and updates

### stakeholder-communicator
**Purpose**: Manage stakeholder communication and reporting
- Manages communication with project stakeholders
- Creates status reports and project updates
- Facilitates stakeholder meetings and feedback sessions
- Ensures stakeholder alignment and satisfaction

### risk-manager
**Purpose**: Identify, assess, and mitigate project risks
- Identifies potential project risks and issues
- Assesses risk impact and probability
- Develops risk mitigation strategies
- Monitors and reports on risk status

### training-change-manager
**Purpose**: Manage training and change management processes
- Creates training materials and documentation
- Manages system adoption and change processes
- Facilitates user training and support
- Ensures smooth transition to new systems

## Usage Patterns

These agents coordinate project execution:
1. **project-orchestrator** provides overall coordination
2. **project-planner** creates detailed plans
3. **risk-manager** identifies and mitigates risks
4. **stakeholder-communicator** manages communication
5. **training-change-manager** handles adoption
6. **project-template-manager** provides reusable patterns

## Comprehensive Collaboration Framework

### Project Coordination Hub
- **project-orchestrator** coordinates with ALL agent categories for end-to-end execution
- **project-planner** collaborates with technical agents for realistic planning
- **stakeholder-communicator** interfaces with all agents for comprehensive reporting

### Internal Project Management Collaboration
- **project-orchestrator** ↔ **project-planner**: Overall coordination and detailed planning
- **project-planner** ↔ **risk-manager**: Risk-informed planning and mitigation
- **stakeholder-communicator** ↔ **All PM agents**: Communication and reporting coordination
- **training-change-manager** ↔ **uat-coordinator**: User adoption and acceptance testing

### Cross-Category Integration Patterns
- **With Requirements**: Uses requirements for comprehensive project planning
- **With Architecture**: Coordinates system design and implementation planning
- **With Development**: Manages development timelines and resource allocation
- **With Quality Assurance**: Ensures project quality gates and testing coordination
- **With Security**: Integrates security requirements and compliance planning
- **With Operations**: Manages deployment planning and operational readiness
- **With Documentation**: Coordinates documentation creation and stakeholder communication

## Master Coordination Patterns

### Project Lifecycle Coordination
1. **Requirements → Planning**: project-planner works with requirements-analyst
2. **Planning → Architecture**: project-orchestrator coordinates with system-architect
3. **Architecture → Development**: Coordinates implementation across all dev agents
4. **Development → Quality**: Manages testing and review processes
5. **Quality → Deployment**: Coordinates with operations for production readiness
6. **Deployment → Training**: training-change-manager handles user adoption

### Continuous Coordination
- **Progress Tracking**: Continuous monitoring across all project phases
- **Risk Management**: Ongoing risk assessment and mitigation
- **Stakeholder Communication**: Regular updates and feedback collection
- **Quality Gates**: Ensuring standards are met at every phase

## Integration with Other Categories

- **Requirements Analysis**: Foundation for all project planning and execution
- **All Technical Categories**: Coordinates work across all technical domains
- **Quality Assurance**: Ensures project quality gates and testing standards
- **Operations**: Manages deployment and operational readiness
- **Documentation**: Coordinates comprehensive project documentation
- **Meta-Management**: Optimizes project workflows and agent utilization
