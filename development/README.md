# Development & Implementation Agents

This category contains agents specialized in building high-quality, maintainable code across multiple programming languages and frameworks. Each agent includes comprehensive collaboration patterns with other agents to ensure seamless development workflows.

## Agents in this Category (11 agents)

### nextjs-developer
**Purpose**: Specialized Next.js application development
- Expert in Next.js framework and React ecosystem
- Implements modern web applications with best practices
- Handles routing, state management, and optimization
- Ensures performance and SEO optimization

### nodejs-developer
**Purpose**: Node.js backend development and JavaScript ecosystem
- Expert in Node.js runtime and JavaScript/TypeScript development
- Builds backend APIs, microservices, and real-time applications
- Implements Express.js, GraphQL, and WebSocket solutions
- Handles authentication, databases, and performance optimization

### nestjs-developer
**Purpose**: Enterprise-grade Node.js applications using NestJS framework
- Expert in NestJS framework with TypeScript and dependency injection
- Builds scalable backend systems with modular architecture
- Implements advanced features like guards, interceptors, and pipes
- Handles microservices, GraphQL APIs, and real-time applications
- Specializes in enterprise patterns and complex backend systems

### python-developer
**Purpose**: Comprehensive Python application development
- Expert in Python language features and ecosystem
- Builds web applications using Django, Flask, FastAPI
- Handles data processing, automation, and API development
- Implements Python best practices and testing strategies

### golang-developer
**Purpose**: Go application development and concurrent programming
- Expert in Go language features and concurrency patterns
- Builds high-performance backend services and microservices
- Implements efficient CLI tools and system programming
- Handles Go-specific optimization and performance tuning

### ruby-developer
**Purpose**: Ruby application development and scripting
- Expert in Ruby language features and object-oriented design
- Builds Ruby applications, scripts, and automation tools
- Implements metaprogramming and DSL development
- Handles Ruby gem development and ecosystem integration

### rails-developer
**Purpose**: Ruby on Rails web application development
- Expert in Rails framework and MVC architecture
- Builds scalable web applications and APIs with Rails
- Implements ActiveRecord, routing, and Rails conventions
- Handles Rails-specific features and deployment strategies

### sql-developer
**Purpose**: Advanced SQL development and database programming
- Expert in complex SQL queries and optimization
- Implements stored procedures, functions, and triggers
- Handles advanced SQL features and performance tuning
- Provides database programming and data analysis expertise

### code-refactoring-specialist
**Purpose**: Improve code quality, performance, and maintainability
- Safely refactors existing codebases
- Reduces technical debt and improves code structure
- Optimizes performance and maintainability
- Preserves functionality while improving design

### dependency-manager
**Purpose**: Manage project dependencies and version compatibility
- Manages package dependencies and versions
- Resolves dependency conflicts and compatibility issues
- Ensures security and stability of dependencies
- Optimizes dependency tree and bundle size

### kubernetes-developer
**Purpose**: Kubernetes application development and cloud-native patterns
- Expert in Kubernetes manifests, Helm charts, and operators
- Builds cloud-native applications following 12-factor principles
- Implements service mesh integration and microservices patterns
- Handles Kubernetes-native CI/CD and GitOps workflows

### technical-research-analyst
**Purpose**: Research technologies and provide implementation guidance
- Researches new technologies and frameworks
- Provides implementation guidance and best practices
- Analyzes technology trade-offs and recommendations
- Stays current with development trends and tools

## Usage Patterns

These agents support the development lifecycle:
1. **technical-research-analyst** researches optimal technologies and frameworks
2. **Language-specific developers** implement applications in their respective languages:
   - **nodejs-developer** for Node.js backend services and JavaScript development
   - **nestjs-developer** for enterprise-grade NestJS applications with advanced patterns
   - **python-developer** for Python applications and data processing
   - **golang-developer** for Go services and concurrent programming
   - **ruby-developer** for Ruby applications and scripting
   - **rails-developer** for Rails web applications
   - **nextjs-developer** for Next.js web applications
3. **kubernetes-developer** handles cloud-native application development and Kubernetes deployment
4. **sql-developer** handles database programming and query optimization
5. **dependency-manager** maintains project dependencies across languages
6. **code-refactoring-specialist** improves code quality over time

## Comprehensive Collaboration Framework

### Universal Development Patterns
All development agents collaborate with:
- **technical-research-analyst** for technology research and evaluation
- **code-reviewer** for comprehensive code quality assessment
- **security-analyzer** for security vulnerability assessment
- **test-suite-generator** for testing strategy and test creation
- **dependency-manager** for package management and security
- **performance-optimizer** for performance optimization

### Language-Specific Collaborations
- **nodejs-developer** ↔ **nestjs-developer**: Node.js ecosystem and enterprise framework development
- **nodejs-developer** ↔ **nextjs-developer**: Full-stack JavaScript development
- **nestjs-developer** ↔ **nextjs-developer**: Full-stack TypeScript applications
- **ruby-developer** ↔ **rails-developer**: Ruby ecosystem development
- **All language developers** ↔ **sql-developer**: Database integration
- **kubernetes-developer** ↔ **All language developers**: Containerization

### Quality & Architecture Integration
- **code-refactoring-specialist** ↔ **All developers**: Code improvement and modernization
- **All developers** ↔ **system-architect**: Architecture implementation
- **All developers** ↔ **api-designer**: API implementation

## Integration with Other Categories

- **Design & Architecture**: Implements architectural designs and system specifications
- **Quality Assurance**: Works with testing and review processes for comprehensive quality
- **Security**: Implements security measures and follows security best practices
- **Operations**: Prepares code for deployment with proper containerization and CI/CD
- **Project Management**: Coordinates with project planning and stakeholder communication
- **Documentation**: Creates technical documentation and implementation guides
