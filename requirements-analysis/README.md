# Requirements & Analysis Agents

This category contains agents specialized in transforming business needs into detailed technical specifications.

## Agents in this Category

### requirements-analyst
**Purpose**: Analyze user needs and create detailed functional specifications
- Gathers and analyzes business requirements
- Creates comprehensive functional specifications
- Identifies stakeholder needs and constraints
- Translates business language into technical requirements

### user-story-generator
**Purpose**: Generate comprehensive user stories and acceptance criteria
- Creates detailed user stories following best practices
- Develops acceptance criteria for each story
- Ensures stories are testable and implementable
- Maintains consistency across the product backlog

### business-process-analyst
**Purpose**: Analyze business processes and translate to technical requirements
- Maps existing business processes
- Identifies automation opportunities
- Translates process flows into system requirements
- Ensures technical solutions align with business workflows

### requirements-validator
**Purpose**: Validate requirements completeness and consistency
- Reviews requirements for completeness and clarity
- Identifies conflicts and inconsistencies
- Ensures requirements are testable and measurable
- Validates alignment with business objectives

## Usage Patterns

These agents typically work together in sequence:
1. **requirements-analyst** gathers initial requirements
2. **business-process-analyst** maps processes to technical needs
3. **user-story-generator** creates implementable user stories
4. **requirements-validator** ensures quality and completeness

## Integration with Other Categories

- **Design & Architecture**: Provides input for system design decisions
- **Quality Assurance**: Defines acceptance criteria for testing
- **Project Management**: Informs project planning and scope definition
